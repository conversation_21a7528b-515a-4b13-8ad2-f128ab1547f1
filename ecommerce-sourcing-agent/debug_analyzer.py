#!/usr/bin/env python3
"""
Debug Analyzer for E-commerce Product Sourcing Agent

This utility helps analyze the exported SerpAPI responses and processed results
for debugging and optimization purposes.
"""

import os
import json
import glob
from datetime import datetime
from typing import Dict, List, Any
import argparse


def analyze_api_responses(export_dir: str = "debug_exports") -> None:
    """
    Analyze all exported API responses in the directory.
    
    Args:
        export_dir: Directory containing the exported files
    """
    print(f"🔍 正在分析API响应：{export_dir}")

    # Find all API response files
    api_files = glob.glob(os.path.join(export_dir, "serpapi_response_*.json"))
    processed_files = glob.glob(os.path.join(export_dir, "processed_results_*.json"))

    print(f"找到 {len(api_files)} 个API响应文件")
    print(f"找到 {len(processed_files)} 个处理结果文件")

    if not api_files:
        print("未找到API响应文件。")
        return

    # Analyze each API response
    for api_file in sorted(api_files):
        print(f"\n📄 正在分析：{os.path.basename(api_file)}")
        
        try:
            with open(api_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Extract key information
            timestamp = data.get("timestamp", "Unknown")
            search_query = data.get("search_query", "Unknown")
            raw_response = data.get("raw_response", {})
            metadata = data.get("metadata", {})
            
            print(f"   时间戳：{timestamp}")
            print(f"   搜索查询：{search_query}")
            print(f"   总结果数：{metadata.get('total_results', 0)}")
            print(f"   有错误：{metadata.get('has_error', False)}")

            # Analyze search information
            search_info = raw_response.get("search_information", {})
            if search_info:
                print(f"   亚马逊总结果数：{search_info.get('total_results', 'N/A')}")
                print(f"   页面：{search_info.get('page', 'N/A')}")

            # Analyze organic results
            organic_results = raw_response.get("organic_results", [])
            if organic_results:
                print(f"   有机搜索结果数量：{len(organic_results)}")
                
                # Price analysis
                prices = []
                ratings = []
                for result in organic_results:
                    if "extracted_price" in result:
                        prices.append(result["extracted_price"])
                    if "rating" in result:
                        ratings.append(result["rating"])
                
                if prices:
                    print(f"   价格范围：${min(prices):.2f} - ${max(prices):.2f}")
                    print(f"   平均价格：${sum(prices)/len(prices):.2f}")

                if ratings:
                    print(f"   评分范围：{min(ratings):.1f} - {max(ratings):.1f}")
                    print(f"   平均评分：{sum(ratings)/len(ratings):.1f}")

                # Count special attributes
                prime_count = len([r for r in organic_results if r.get("prime", False)])
                sponsored_count = len([r for r in organic_results if r.get("sponsored", False)])
                print(f"   Prime产品：{prime_count}")
                print(f"   赞助产品：{sponsored_count}")

        except Exception as e:
            print(f"   ❌ 分析文件时出错：{e}")


def analyze_processed_results(export_dir: str = "debug_exports") -> None:
    """
    Analyze all processed result files in the directory.
    
    Args:
        export_dir: Directory containing the exported files
    """
    print(f"\n📊 正在分析处理结果：{export_dir}")

    # Find all processed result files
    processed_files = glob.glob(os.path.join(export_dir, "processed_results_*.json"))

    if not processed_files:
        print("未找到处理结果文件。")
        return

    # Analyze each processed result file
    for processed_file in sorted(processed_files):
        print(f"\n📄 正在分析：{os.path.basename(processed_file)}")
        
        try:
            with open(processed_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Extract key information
            timestamp = data.get("timestamp", "Unknown")
            search_query = data.get("search_query", "Unknown")
            total_products = data.get("total_products", 0)
            summary = data.get("summary", {})
            
            print(f"   时间戳：{timestamp}")
            print(f"   搜索查询：{search_query}")
            print(f"   总产品数：{total_products}")

            # Analyze summary statistics
            if summary:
                price_range = summary.get("price_range", {})
                rating_range = summary.get("rating_range", {})

                print(f"   价格范围：${price_range.get('min', 0):.2f} - ${price_range.get('max', 0):.2f}")
                print(f"   平均价格：${price_range.get('avg', 0):.2f}")
                print(f"   评分范围：{rating_range.get('min', 0):.1f} - {rating_range.get('max', 0):.1f}")
                print(f"   平均评分：{rating_range.get('avg', 0):.1f}")
                print(f"   Prime产品：{summary.get('prime_products', 0)}")
                print(f"   赞助产品：{summary.get('sponsored_products', 0)}")
            
            # Analyze individual products
            products = data.get("processed_products", [])
            if products:
                print(f"   产品详情：")
                for i, product in enumerate(products, 1):
                    title = product.get("title", "Unknown")[:60] + "..."
                    price = product.get("price", 0)
                    rating = product.get("rating", 0)
                    reviews = product.get("reviews_count", 0)
                    asin = product.get("asin", "N/A")
                    print(f"     {i}. {title}")
                    print(f"        价格：${price}，评分：{rating}（{reviews}条评论），ASIN：{asin}")

        except Exception as e:
            print(f"   ❌ 分析文件时出错：{e}")


def clean_old_exports(export_dir: str = "debug_exports", days_old: int = 7) -> None:
    """
    Clean up old export files.
    
    Args:
        export_dir: Directory containing the exported files
        days_old: Remove files older than this many days
    """
    print(f"\n🧹 正在清理超过{days_old}天的导出文件...")

    all_files = glob.glob(os.path.join(export_dir, "*.json"))
    current_time = datetime.now()
    removed_count = 0

    for file_path in all_files:
        try:
            file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            age_days = (current_time - file_time).days

            if age_days > days_old:
                os.remove(file_path)
                print(f"   已删除：{os.path.basename(file_path)}（年龄：{age_days}天）")
                removed_count += 1
        except Exception as e:
            print(f"   ❌ 删除{file_path}时出错：{e}")

    print(f"✅ 已删除{removed_count}个旧文件")


def main():
    """Main function to run the debug analyzer."""
    parser = argparse.ArgumentParser(description="Analyze SerpAPI debug exports")
    parser.add_argument("--dir", default="debug_exports", help="Export directory to analyze")
    parser.add_argument("--clean", type=int, metavar="DAYS", help="Clean files older than DAYS")
    parser.add_argument("--api-only", action="store_true", help="Analyze only API responses")
    parser.add_argument("--processed-only", action="store_true", help="Analyze only processed results")
    
    args = parser.parse_args()
    
    print("🔍 电商代理调试分析器")
    print("=" * 50)

    if args.clean:
        clean_old_exports(args.dir, args.clean)
        return

    if not args.processed_only:
        analyze_api_responses(args.dir)

    if not args.api_only:
        analyze_processed_results(args.dir)

    print("\n✅ 分析完成！")


if __name__ == "__main__":
    main()
