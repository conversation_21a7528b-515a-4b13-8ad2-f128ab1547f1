import streamlit as st
import os
import sys
import logging
import traceback
import time
import threading
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('app.log')
    ]
)
logger = logging.getLogger(__name__)

# Disable Streamlit's static file server
os.environ["STREAMLIT_SERVER_HEADLESS"] = "true"
os.environ["STREAMLIT_SERVER_ENABLE_STATIC_SERVE"] = "false"

# Add the current directory to the path so we can import the agent
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from ecommerce_sourcing_agent import create_ecommerce_sourcing_agent, ProgressStep

# Set page config with CDN resources
st.set_page_config(
    page_title="电商采购助手",
    page_icon="🛍️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Add optimized inline CSS
st.markdown("""
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
        
        :root {
            --font-sans: 'Noto Sans SC', sans-serif;
            --primary-color: #4CAF50;
            --primary-hover: #45a049;
            --assistant-bg: #e3f2fd;
            --user-bg: #f0f2f6;
        }
        
        body, button, input, select, textarea, [class*="st"] {
            font-family: var(--font-sans) !important;
        }
        
        /* Disable Streamlit's default font loading */
        link[rel="preload"][as="font"],
        .stApp > iframe[title="streamlitApp"] {
            display: none !important;
        }
        
        .stTextInput > div > div > input {
            border-radius: 20px;
            padding: 10px 15px;
            width: 100%;
        }
        
        .stButton > button {
            width: 100%;
            border-radius: 20px;
            padding: 10px 24px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            font-weight: 500;
            transition: all 0.2s ease;
            cursor: pointer;
            font-size: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stButton > button:hover {
            background-color: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .chat-message {
            padding: 0.75rem 1rem;
            border-radius: 1rem;
            margin: 0.5rem 0;
            max-width: 85%;
            line-height: 1.5;
            position: relative;
            animation: fadeIn 0.3s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .chat-message.user {
            background-color: var(--user-bg);
            margin-left: 15%;
            border-bottom-right-radius: 0.25rem;
        }
        
        .chat-message.assistant {
            background-color: var(--assistant-bg);
            margin-right: 15%;
            border-bottom-left-radius: 0.25rem;
        }
        
        .stSpinner > div {
            margin: 1rem auto;
            width: 2rem;
            height: 2rem;
            border-width: 0.2rem;
        }
        
        /* Message container styles */
        [data-testid="stChatMessage"] {
            padding: 0.5rem 1rem;
        }
        
        /* Typing indicator */
        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .typing-indicator {
            display: flex;
            gap: 0.5rem;
            padding: 0.5rem 0;
        }
        
        .typing-dot {
            width: 8px;
            height: 8px;
            background-color: #666;
            border-radius: 50%;
            display: inline-block;
            animation: blink 1.4s infinite both;
        }
        
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .chat-message {
                max-width: 90%;
                margin-left: 10% !important;
                margin-right: 0 !important;
                font-size: 0.95rem;
            }
            
            .chat-message.user {
                margin-left: 10% !important;
                margin-right: 0 !important;
            }
        }
    </style>
""", unsafe_allow_html=True)

# Initialize session state for chat history
if "messages" not in st.session_state:
    st.session_state.messages = [
        {
            "role": "assistant",
            "content": "👋 您好！我是您的电商选品助手。请告诉我您想找什么类型的产品？我会帮您分析热销商品并寻找优质供应商。"
        }
    ]

# Initialize session state for progress tracking
if "progress_data" not in st.session_state:
    st.session_state.progress_data = {
        "message": "",
        "step": None,
        "is_active": False,
        "last_update": 0
    }

if "agent_running" not in st.session_state:
    st.session_state.agent_running = False

if "last_rendered_update" not in st.session_state:
    st.session_state.last_rendered_update = 0

# Initialize agent in session state if not exists
if 'agent' not in st.session_state:
    logger.info("Creating new ecommerce sourcing agent...")
    try:
        st.session_state.agent = create_ecommerce_sourcing_agent()
        logger.info("Agent created successfully")
    except Exception as e:
        logger.error(f"Failed to create agent: {e}")
        st.error(f"Failed to initialize agent: {e}")
        st.stop()

agent = st.session_state.agent

class ThreadSafeProgress:
    """Thread-safe progress tracker that uses session state for cross-thread communication"""

    def __init__(self):
        self.step_descriptions = {
            # Initial setup
            ProgressStep.START: "🔍 正在分析您的需求...",
            ProgressStep.INITIALIZING_SEARCH: "⚙️ 正在初始化搜索参数...",

            # Amazon search phase
            ProgressStep.PREPARING_AMAZON_SEARCH: "� 正在准备亚马逊搜索参数...",
            ProgressStep.EXECUTING_AMAZON_QUERY: "🔍 正在执行亚马逊产品搜索...",
            ProgressStep.PROCESSING_AMAZON_RESULTS: "📥 正在处理亚马逊搜索结果...",
            ProgressStep.FILTERING_AMAZON_PRODUCTS: "� 正在筛选符合条件的产品...",
            ProgressStep.ANALYZING_AMAZON_DATA: "�📊 正在分析亚马逊产品数据...",

            # 1688 search phase
            ProgressStep.PREPARING_1688_SEARCH: "🏭 正在准备1688供应商搜索...",
            ProgressStep.CONNECTING_1688_API: "🔗 正在连接1688 API接口...",
            ProgressStep.EXECUTING_1688_QUERY: "🔍 正在搜索1688供应商数据...",
            ProgressStep.PROCESSING_1688_RESULTS: "📦 正在处理1688搜索结果...",
            ProgressStep.FILTERING_1688_SUPPLIERS: "🔧 正在筛选优质供应商...",
            ProgressStep.MATCHING_PRODUCTS_SUPPLIERS: "🔗 正在匹配产品与供应商...",

            # Analysis phase
            ProgressStep.CALCULATING_PROFIT_MARGINS: "📈 正在计算利润空间...",
            ProgressStep.ANALYZING_MARKET_POTENTIAL: "📊 正在分析市场潜力...",
            ProgressStep.EVALUATING_SUPPLIER_RELIABILITY: "⭐ 正在评估供应商可靠性...",
            ProgressStep.COMPARING_SHIPPING_COSTS: "🚚 正在比较物流成本...",

            # Report generation
            ProgressStep.GENERATING_PRODUCT_ANALYSIS: "📝 正在生成产品分析报告...",
            ProgressStep.GENERATING_SUPPLIER_RECOMMENDATIONS: "� 正在生成供应商推荐...",
            ProgressStep.FINALIZING_REPORT: "📋 正在完善最终报告...",

            # Completion
            ProgressStep.COMPLETED: "✅ 分析完成！"
        }

    def update_from_thread(self, message: str, step: ProgressStep):
        """Thread-safe method to update progress from background threads"""
        try:
            # Update session state (thread-safe)
            st.session_state.progress_data = {
                "message": message,
                "step": step,
                "is_active": True,
                "last_update": time.time()
            }
            logger.info(f"Progress updated in session state: {step.name} - {message}")
        except Exception as e:
            logger.warning(f"Failed to update progress in session state: {e}")

    def render_from_session_state(self, message_placeholder):
        """Render progress from session state (called from main thread)"""
        try:
            progress_data = st.session_state.progress_data
            if not progress_data.get("is_active") or not progress_data.get("step"):
                return

            step = progress_data["step"]
            message = progress_data["message"]

            # Get the current step description
            step_desc = self.step_descriptions.get(step, "")

            # Create a progress bar
            total_steps = len(ProgressStep) - 1  # Exclude COMPLETED
            current_progress = (step.value / total_steps) * 100

            # Build the display content
            content = f"""
            <div style="margin: 10px 0; padding: 10px; border-radius: 5px; background: #f8f9fa;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                    <strong>{step_desc}</strong>
                    <span>{int(current_progress)}%</span>
                </div>
                <div style="height: 8px; background: #e9ecef; border-radius: 4px; overflow: hidden;">
                    <div style="height: 100%; width: {current_progress}%; background: #00b4d8; transition: width 0.3s;"></div>
                </div>
                <div style="margin-top: 8px; font-size: 0.9em; color: #495057;">
                    {message}
                </div>
            </div>
            """

            message_placeholder.markdown(content, unsafe_allow_html=True)

        except Exception as e:
            logger.warning(f"Failed to render progress: {e}")

class AgentProgress:
    """Legacy progress class for backward compatibility"""
    def __init__(self, message_placeholder):
        self.message_placeholder = message_placeholder
        self.thread_safe_progress = ThreadSafeProgress()

    def update(self, message: str, step: ProgressStep):
        """Update progress - now thread-safe"""
        self.thread_safe_progress.update_from_thread(message, step)

def display_agent_progress(message_placeholder, user_input):
    """Initialize and return a progress tracker"""
    progress = AgentProgress(message_placeholder)
    progress.update(f"开始处理您的请求: {user_input}", ProgressStep.START)
    return progress

def create_progress_monitor(message_placeholder):
    """Create a progress monitor that polls session state for updates"""
    progress_renderer = ThreadSafeProgress()

    # Create a container for the progress display
    progress_container = message_placeholder.container()

    def monitor_progress():
        """Monitor and render progress updates from session state"""
        last_update_time = 0

        while st.session_state.get("agent_running", False):
            try:
                current_time = st.session_state.progress_data.get("last_update", 0)

                # Only update if there's new progress data
                if current_time > last_update_time:
                    with progress_container:
                        progress_renderer.render_from_session_state(st.empty())
                    last_update_time = current_time

                time.sleep(0.1)  # Poll every 100ms

            except Exception as e:
                logger.warning(f"Progress monitor error: {e}")
                break

    return monitor_progress

def display_chat():
    """Display chat messages"""
    for message in st.session_state.messages:
        with st.container():
            col1, col2 = st.columns([1, 20])
            with col1:
                st.markdown(f'<div class="avatar">{"👤" if message["role"] == "user" else "🤖"}</div>', unsafe_allow_html=True)
            with col2:
                if message["role"] == "assistant":
                    st.markdown(f'<div class="chat-message assistant"><div class="content">{message["content"]}</div></div>', unsafe_allow_html=True)
                else:
                    st.markdown(f'<div class="chat-message user"><div class="content">{message["content"]}</div></div>', unsafe_allow_html=True)

def main():
    # Display chat messages
    display_chat()

    # Chat input
    user_input = st.chat_input("请输入您想找的产品...")
    
    if user_input:
        logger.info(f"User input received: {user_input}")

        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": user_input})

        # Display user message
        with st.chat_message("user"):
            st.markdown(user_input)

        # Display assistant's response area
        with st.chat_message("assistant"):
            message_placeholder = st.empty()

            # Set agent running state
            st.session_state.agent_running = True

            # Initialize progress in session state
            st.session_state.progress_data = {
                "message": f"开始处理您的请求: {user_input}",
                "step": ProgressStep.START,
                "is_active": True,
                "last_update": time.time()
            }

            # Create progress display containers
            progress_container = st.empty()
            status_container = st.empty()

            # Initialize progress display
            current_step = ProgressStep.START
            progress_value = 0
            total_steps = len(ProgressStep) - 1

            def update_progress_display(message: str, step: ProgressStep):
                nonlocal current_step, progress_value
                current_step = step
                progress_value = (step.value / total_steps) * 100

                # Get step description
                step_desc = thread_safe_progress.step_descriptions.get(step, "")

                # Update progress bar
                with progress_container.container():
                    st.progress(progress_value / 100)
                    st.write(f"**{step_desc}**")
                    st.write(f"📊 进度: {int(progress_value)}%")
                    if message:
                        st.write(f"� {message}")

            # Show initial progress
            update_progress_display("开始处理您的请求", ProgressStep.START)

            try:
                logger.info("Starting agent processing...")

                # Create thread-safe progress tracker
                thread_safe_progress = ThreadSafeProgress()

                # Progress callback that updates UI immediately
                def progress_callback(message: str, step: ProgressStep):
                    logger.info(f"Progress update: {step.name} - {message}")
                    # Update session state
                    st.session_state.progress_data = {
                        "message": message,
                        "step": step,
                        "is_active": True,
                        "last_update": time.time()
                    }
                    # Update UI immediately
                    update_progress_display(message, step)

                # Set the progress callback in the agent
                from ecommerce_sourcing_agent import set_progress_callback
                set_progress_callback(progress_callback)

                logger.info("Calling agent()...")

                # Call agent directly (blocking)
                response = agent(user_input)

                # Show completion
                update_progress_display("分析完成！", ProgressStep.COMPLETED)
                time.sleep(1)  # Show completion briefly

                # Clear progress display
                progress_container.empty()
                status_container.empty()

                logger.info(f"Agent response received: {type(response)}")
                logger.info(f"Response content: {response.content if hasattr(response, 'content') else str(response)}")

                # Get response content
                if hasattr(response, 'content'):
                    response_content = response.content
                elif hasattr(response, 'text'):
                    response_content = response.text
                else:
                    response_content = str(response)

                # Display the response
                st.markdown(response_content)

                # Add response to chat history
                st.session_state.messages.append({"role": "assistant", "content": response_content})

                logger.info("Response displayed successfully")

            except Exception as e:
                logger.error(f"Error processing request: {e}")
                progress_container.empty()
                status_container.empty()
                error_msg = f"❌ 处理请求时出错: {str(e)}"
                st.markdown(error_msg)
                st.session_state.messages.append({"role": "assistant", "content": error_msg})

if __name__ == "__main__":
    main()
