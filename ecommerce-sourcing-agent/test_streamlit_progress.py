#!/usr/bin/env python3
"""
Simple test to verify Streamlit progress updates work
"""
import streamlit as st
import time
import threading
from enum import Enum, auto

class ProgressStep(Enum):
    START = auto()
    STEP1 = auto()
    STEP2 = auto()
    STEP3 = auto()
    COMPLETED = auto()

# Initialize session state
if "test_progress_data" not in st.session_state:
    st.session_state.test_progress_data = {
        "message": "",
        "step": None,
        "is_active": False,
        "last_update": 0
    }

if "test_running" not in st.session_state:
    st.session_state.test_running = False

if "last_rendered" not in st.session_state:
    st.session_state.last_rendered = 0

st.title("Progress Update Test")

# Auto-refresh mechanism
if st.session_state.test_running:
    current_update = st.session_state.test_progress_data.get("last_update", 0)
    if current_update > st.session_state.last_rendered:
        st.session_state.last_rendered = current_update
        time.sleep(1)
        st.rerun()

# Display current progress
if st.session_state.test_progress_data.get("is_active"):
    step = st.session_state.test_progress_data.get("step")
    message = st.session_state.test_progress_data.get("message", "")
    
    if step:
        progress_value = step.value / len(ProgressStep) * 100
        st.progress(progress_value / 100)
        st.write(f"**{step.name}**: {message}")

def simulate_background_work():
    """Simulate background work with progress updates"""
    steps = [
        (ProgressStep.START, "Starting simulation..."),
        (ProgressStep.STEP1, "Processing step 1..."),
        (ProgressStep.STEP2, "Processing step 2..."),
        (ProgressStep.STEP3, "Processing step 3..."),
        (ProgressStep.COMPLETED, "Simulation complete!")
    ]
    
    for step, message in steps:
        st.session_state.test_progress_data = {
            "message": message,
            "step": step,
            "is_active": True,
            "last_update": time.time()
        }
        time.sleep(2)  # Simulate work
    
    st.session_state.test_running = False
    st.session_state.test_progress_data["is_active"] = False

if st.button("Start Progress Test"):
    st.session_state.test_running = True
    st.session_state.test_progress_data = {
        "message": "Initializing...",
        "step": ProgressStep.START,
        "is_active": True,
        "last_update": time.time()
    }
    
    # Start background thread
    thread = threading.Thread(target=simulate_background_work)
    thread.daemon = True
    thread.start()
    
    st.rerun()

if st.button("Stop Test"):
    st.session_state.test_running = False
    st.session_state.test_progress_data["is_active"] = False
    st.rerun()

st.write("**Debug Info:**")
st.write(f"Test running: {st.session_state.test_running}")
st.write(f"Progress data: {st.session_state.test_progress_data}")
