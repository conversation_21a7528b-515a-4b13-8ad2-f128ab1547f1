#!/usr/bin/env python3
"""
Updated progress step descriptions with proper emojis
"""

from ecommerce_sourcing_agent import ProgressStep

STEP_DESCRIPTIONS = {
    # Initial setup
    ProgressStep.START: "🔍 正在分析您的需求...",
    ProgressStep.INITIALIZING_SEARCH: "⚙️ 正在初始化搜索参数...",

    # Amazon search phase
    ProgressStep.PREPARING_AMAZON_SEARCH: "🛒 正在准备亚马逊搜索参数...",
    ProgressStep.EXECUTING_AMAZON_QUERY: "🔍 正在执行亚马逊产品搜索...",
    ProgressStep.PROCESSING_AMAZON_RESULTS: "📥 正在处理亚马逊搜索结果...",
    ProgressStep.FILTERING_AMAZON_PRODUCTS: "🔧 正在筛选符合条件的产品...",
    ProgressStep.ANALYZING_AMAZON_DATA: "📊 正在分析亚马逊产品数据...",

    # 1688 search phase
    ProgressStep.PREPARING_1688_SEARCH: "🏭 正在准备1688供应商搜索...",
    ProgressStep.CONNECTING_1688_API: "🔗 正在连接1688 API接口...",
    ProgressStep.EXECUTING_1688_QUERY: "🔍 正在搜索1688供应商数据...",
    ProgressStep.PROCESSING_1688_RESULTS: "📦 正在处理1688搜索结果...",
    ProgressStep.FILTERING_1688_SUPPLIERS: "🔧 正在筛选优质供应商...",
    ProgressStep.MATCHING_PRODUCTS_SUPPLIERS: "🔗 正在匹配产品与供应商...",

    # Analysis phase
    ProgressStep.CALCULATING_PROFIT_MARGINS: "📈 正在计算利润空间...",
    ProgressStep.ANALYZING_MARKET_POTENTIAL: "📊 正在分析市场潜力...",
    ProgressStep.EVALUATING_SUPPLIER_RELIABILITY: "⭐ 正在评估供应商可靠性...",
    ProgressStep.COMPARING_SHIPPING_COSTS: "🚚 正在比较物流成本...",

    # Report generation
    ProgressStep.GENERATING_PRODUCT_ANALYSIS: "📝 正在生成产品分析报告...",
    ProgressStep.GENERATING_SUPPLIER_RECOMMENDATIONS: "💡 正在生成供应商推荐...",
    ProgressStep.FINALIZING_REPORT: "📋 正在完善最终报告...",

    # Completion
    ProgressStep.COMPLETED: "✅ 分析完成！"
}
