import streamlit as st
import os
import sys
import logging
import traceback
import time
import threading
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('app.log')
    ]
)
logger = logging.getLogger(__name__)

# Disable Streamlit's static file server
os.environ["STREAMLIT_SERVER_HEADLESS"] = "true"
os.environ["STREAMLIT_SERVER_ENABLE_STATIC_SERVE"] = "false"

# Add the current directory to the path so we can import the agent
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from ecommerce_sourcing_agent import create_ecommerce_sourcing_agent, ProgressStep

# Set page config with CDN resources
st.set_page_config(
    page_title="电商采购助手",
    page_icon="🛍️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Add optimized inline CSS
st.markdown("""
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
        
        :root {
            --font-sans: 'Noto Sans SC', sans-serif;
            --primary-color: #4CAF50;
            --primary-hover: #45a049;
            --assistant-bg: #e3f2fd;
            --user-bg: #f0f2f6;
        }
        
        body, button, input, select, textarea, [class*="st"] {
            font-family: var(--font-sans) !important;
        }
        
        /* Disable Streamlit's default font loading */
        link[rel="preload"][as="font"],
        .stApp > iframe[title="streamlitApp"] {
            display: none !important;
        }
        
        .stTextInput > div > div > input {
            border-radius: 20px;
            padding: 10px 15px;
            width: 100%;
        }
        
        .stButton > button {
            width: 100%;
            border-radius: 20px;
            padding: 10px 24px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            font-weight: 500;
            transition: all 0.2s ease;
            cursor: pointer;
            font-size: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stButton > button:hover {
            background-color: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .chat-message {
            padding: 0.75rem 1rem;
            border-radius: 1rem;
            margin: 0.5rem 0;
            max-width: 85%;
            line-height: 1.5;
            position: relative;
            animation: fadeIn 0.3s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .chat-message.user {
            background-color: var(--user-bg);
            margin-left: 15%;
            border-bottom-right-radius: 0.25rem;
        }
        
        .chat-message.assistant {
            background-color: var(--assistant-bg);
            margin-right: 15%;
            border-bottom-left-radius: 0.25rem;
        }
        
        .stSpinner > div {
            margin: 1rem auto;
            width: 2rem;
            height: 2rem;
            border-width: 0.2rem;
        }
        
        /* Message container styles */
        [data-testid="stChatMessage"] {
            padding: 0.5rem 1rem;
        }
        
        /* Typing indicator */
        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .typing-indicator {
            display: flex;
            gap: 0.5rem;
            padding: 0.5rem 0;
        }
        
        .typing-dot {
            width: 8px;
            height: 8px;
            background-color: #666;
            border-radius: 50%;
            display: inline-block;
            animation: blink 1.4s infinite both;
        }
        
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .chat-message {
                max-width: 90%;
                margin-left: 10% !important;
                margin-right: 0 !important;
                font-size: 0.95rem;
            }
            
            .chat-message.user {
                margin-left: 10% !important;
                margin-right: 0 !important;
            }
        }
    </style>
""", unsafe_allow_html=True)

# Initialize session state for chat history
if "messages" not in st.session_state:
    st.session_state.messages = [
        {
            "role": "assistant",
            "content": "👋 您好！我是您的电商选品助手。请告诉我您想找什么类型的产品？我会帮您分析热销商品并寻找优质供应商。"
        }
    ]

# Initialize session state for progress tracking
if "progress_data" not in st.session_state:
    st.session_state.progress_data = {
        "message": "",
        "step": None,
        "is_active": False,
        "last_update": 0
    }

if "agent_running" not in st.session_state:
    st.session_state.agent_running = False

if "last_rendered_update" not in st.session_state:
    st.session_state.last_rendered_update = 0

# Initialize agent in session state if not exists
if 'agent' not in st.session_state:
    logger.info("Creating new ecommerce sourcing agent...")
    try:
        st.session_state.agent = create_ecommerce_sourcing_agent()
        logger.info("Agent created successfully")
    except Exception as e:
        logger.error(f"Failed to create agent: {e}")
        st.error(f"Failed to initialize agent: {e}")
        st.stop()

agent = st.session_state.agent

class ThreadSafeProgress:
    """Thread-safe progress tracker that uses session state for cross-thread communication"""

    def __init__(self):
        self.step_descriptions = {
            ProgressStep.START: "🔍 正在分析您的需求...",
            ProgressStep.SEARCHING_AMAZON: "🔍 正在搜索亚马逊热销产品...",
            ProgressStep.ANALYZING_PRODUCTS: "📊 正在分析产品数据...",
            ProgressStep.SEARCHING_1688: "🏭 正在1688上寻找供应商...",
            ProgressStep.CALCULATING_PROFITS: "📈 正在计算利润空间...",
            ProgressStep.GENERATING_REPORT: "📝 正在生成详细报告...",
            ProgressStep.COMPLETED: "✅ 分析完成！"
        }

    def update_from_thread(self, message: str, step: ProgressStep):
        """Thread-safe method to update progress from background threads"""
        try:
            # Update session state (thread-safe)
            st.session_state.progress_data = {
                "message": message,
                "step": step,
                "is_active": True,
                "last_update": time.time()
            }
            logger.info(f"Progress updated in session state: {step.name} - {message}")
        except Exception as e:
            logger.warning(f"Failed to update progress in session state: {e}")

    def render_from_session_state(self, message_placeholder):
        """Render progress from session state (called from main thread)"""
        try:
            progress_data = st.session_state.progress_data
            if not progress_data.get("is_active") or not progress_data.get("step"):
                return

            step = progress_data["step"]
            message = progress_data["message"]

            # Get the current step description
            step_desc = self.step_descriptions.get(step, "")

            # Create a progress bar
            total_steps = len(ProgressStep) - 1  # Exclude COMPLETED
            current_progress = (step.value / total_steps) * 100

            # Build the display content
            content = f"""
            <div style="margin: 10px 0; padding: 10px; border-radius: 5px; background: #f8f9fa;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                    <strong>{step_desc}</strong>
                    <span>{int(current_progress)}%</span>
                </div>
                <div style="height: 8px; background: #e9ecef; border-radius: 4px; overflow: hidden;">
                    <div style="height: 100%; width: {current_progress}%; background: #00b4d8; transition: width 0.3s;"></div>
                </div>
                <div style="margin-top: 8px; font-size: 0.9em; color: #495057;">
                    {message}
                </div>
            </div>
            """

            message_placeholder.markdown(content, unsafe_allow_html=True)

        except Exception as e:
            logger.warning(f"Failed to render progress: {e}")

class AgentProgress:
    """Legacy progress class for backward compatibility"""
    def __init__(self, message_placeholder):
        self.message_placeholder = message_placeholder
        self.thread_safe_progress = ThreadSafeProgress()

    def update(self, message: str, step: ProgressStep):
        """Update progress - now thread-safe"""
        self.thread_safe_progress.update_from_thread(message, step)

def display_agent_progress(message_placeholder, user_input):
    """Initialize and return a progress tracker"""
    progress = AgentProgress(message_placeholder)
    progress.update(f"开始处理您的请求: {user_input}", ProgressStep.START)
    return progress

def create_progress_monitor(message_placeholder):
    """Create a progress monitor that polls session state for updates"""
    progress_renderer = ThreadSafeProgress()

    # Create a container for the progress display
    progress_container = message_placeholder.container()

    def monitor_progress():
        """Monitor and render progress updates from session state"""
        last_update_time = 0

        while st.session_state.get("agent_running", False):
            try:
                current_time = st.session_state.progress_data.get("last_update", 0)

                # Only update if there's new progress data
                if current_time > last_update_time:
                    with progress_container:
                        progress_renderer.render_from_session_state(st.empty())
                    last_update_time = current_time

                time.sleep(0.1)  # Poll every 100ms

            except Exception as e:
                logger.warning(f"Progress monitor error: {e}")
                break

    return monitor_progress

def display_chat():
    """Display chat messages"""
    for message in st.session_state.messages:
        with st.container():
            col1, col2 = st.columns([1, 20])
            with col1:
                st.markdown(f'<div class="avatar">{"👤" if message["role"] == "user" else "🤖"}</div>', unsafe_allow_html=True)
            with col2:
                if message["role"] == "assistant":
                    st.markdown(f'<div class="chat-message assistant"><div class="content">{message["content"]}</div></div>', unsafe_allow_html=True)
                else:
                    st.markdown(f'<div class="chat-message user"><div class="content">{message["content"]}</div></div>', unsafe_allow_html=True)

def main():
    # Display chat messages
    display_chat()

    # Chat input
    user_input = st.chat_input("请输入您想找的产品...")
    
    if user_input:
        logger.info(f"User input received: {user_input}")

        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": user_input})

        # Display user message
        with st.chat_message("user"):
            st.markdown(user_input)

        # Display assistant's response area
        with st.chat_message("assistant"):
            message_placeholder = st.empty()

            # Set agent running state
            st.session_state.agent_running = True

            # Initialize progress in session state
            st.session_state.progress_data = {
                "message": f"开始处理您的请求: {user_input}",
                "step": ProgressStep.START,
                "is_active": True,
                "last_update": time.time()
            }

            try:
                logger.info("Starting agent processing...")

                # Create thread-safe progress callback
                thread_safe_progress = ThreadSafeProgress()

                def progress_callback(message: str, step: ProgressStep):
                    logger.info(f"Progress update: {step.name} - {message}")
                    # Update session state from background thread
                    thread_safe_progress.update_from_thread(message, step)
                    # Try to render progress directly to the placeholder
                    try:
                        # Get the current step description
                        step_desc = thread_safe_progress.step_descriptions.get(step, "")

                        # Create a progress bar
                        total_steps = len(ProgressStep) - 1  # Exclude COMPLETED
                        current_progress = (step.value / total_steps) * 100

                        # Build the display content
                        content = f"""
                        <div style="margin: 10px 0; padding: 10px; border-radius: 5px; background: #f8f9fa;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                <strong>{step_desc}</strong>
                                <span>{int(current_progress)}%</span>
                            </div>
                            <div style="height: 8px; background: #e9ecef; border-radius: 4px; overflow: hidden;">
                                <div style="height: 100%; width: {current_progress}%; background: #00b4d8; transition: width 0.3s;"></div>
                            </div>
                            <div style="margin-top: 8px; font-size: 0.9em; color: #495057;">
                                {message}
                            </div>
                        </div>
                        """

                        message_placeholder.markdown(content, unsafe_allow_html=True)
                        logger.info(f"Progress UI updated successfully: {step.name}")

                    except Exception as e:
                        logger.warning(f"Progress UI update failed: {e}")

                # Set the progress callback in the agent
                from ecommerce_sourcing_agent import set_progress_callback
                set_progress_callback(progress_callback)

                logger.info("Calling agent()...")

                response = None
                error = None

                def agent_call():
                    nonlocal response, error
                    try:
                        response = agent(user_input)
                    except Exception as e:
                        error = e
                    finally:
                        # Ensure agent running state is cleared
                        st.session_state.agent_running = False

                # Start agent call in separate thread with timeout
                agent_thread = threading.Thread(target=agent_call)
                agent_thread.daemon = True
                agent_thread.start()

                # Wait for completion with timeout (5 minutes for complex analysis)
                agent_thread.join(300)

                # Check for timeout
                if agent_thread.is_alive():
                    st.session_state.agent_running = False
                    raise TimeoutError("Agent call timed out after 5 minutes")

                if error:
                    st.session_state.agent_running = False
                    raise error

                logger.info(f"Agent response received: {type(response)}")
                logger.info(f"Response content: {response.content if hasattr(response, 'content') else str(response)}")

                # Update to completed state and clear progress
                st.session_state.agent_running = False
                st.session_state.progress_data["is_active"] = False

                # Show completion briefly
                completion_content = """
                <div style="margin: 10px 0; padding: 10px; border-radius: 5px; background: #d4edda; border: 1px solid #c3e6cb;">
                    <div style="color: #155724; font-weight: bold;">
                        ✅ 分析完成！
                    </div>
                </div>
                """
                message_placeholder.markdown(completion_content, unsafe_allow_html=True)
                time.sleep(1)  # Show completion for 1 second
                message_placeholder.empty()

                # Get response content
                if hasattr(response, 'content'):
                    response_content = response.content
                elif hasattr(response, 'text'):
                    response_content = response.text
                else:
                    response_content = str(response)

                # Display the response
                st.markdown(response_content)

                # Add response to chat history
                st.session_state.messages.append({"role": "assistant", "content": response_content})

                logger.info("Response displayed successfully")

            except TimeoutError as e:
                logger.error(f"Agent call timed out: {e}")
                error_msg = "⏰ 请求处理超时，请稍后重试"
                message_placeholder.markdown(error_msg)
                st.session_state.messages.append({"role": "assistant", "content": error_msg})

            except Exception as e:
                logger.error(f"Error processing request: {e}")
                logger.error(f"Traceback: {traceback.format_exc()}")

                # Show error message
                error_msg = f"❌ 处理请求时出错: {str(e)}"
                message_placeholder.markdown(error_msg)

                # Add error to chat history
                st.session_state.messages.append({"role": "assistant", "content": error_msg})

                # Show detailed error in expander
                with st.expander("错误详情"):
                    st.code(traceback.format_exc())

        # Rerun to update the display
        st.rerun()

if __name__ == "__main__":
    main()
