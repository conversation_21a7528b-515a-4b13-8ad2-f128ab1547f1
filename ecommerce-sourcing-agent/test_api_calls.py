#!/usr/bin/env python3
"""
Test script to verify API calls work correctly with timeout mechanisms
"""

import os
import sys
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ecommerce_sourcing_agent import search_amazon_hot_products, find_1688_suppliers

def test_amazon_search():
    """Test Amazon search with timeout"""
    print("🧪 Testing Amazon search...")
    try:
        start_time = time.time()
        results = search_amazon_hot_products(
            product_category="sports",
            keywords="basketball shoes",
            max_results=3
        )
        end_time = time.time()
        
        print(f"✅ Amazon search completed in {end_time - start_time:.2f} seconds")
        print(f"   Found {len(results)} products")
        
        if results:
            print(f"   First product: {results[0].get('title', 'No title')[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Amazon search failed: {e}")
        return False

def test_1688_search():
    """Test 1688 search with timeout"""
    print("\n🧪 Testing 1688 search...")
    try:
        start_time = time.time()
        results = find_1688_suppliers(
            product_title="basketball shoes",
            max_suppliers=3
        )
        end_time = time.time()
        
        print(f"✅ 1688 search completed in {end_time - start_time:.2f} seconds")
        print(f"   Found {len(results)} suppliers")
        
        if results:
            print(f"   First supplier: {results[0].get('supplier_name', 'No name')[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 1688 search failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting API timeout tests...\n")
    
    amazon_ok = test_amazon_search()
    suppliers_ok = test_1688_search()
    
    print(f"\n📊 Test Results:")
    print(f"   Amazon Search: {'✅ PASS' if amazon_ok else '❌ FAIL'}")
    print(f"   1688 Search: {'✅ PASS' if suppliers_ok else '❌ FAIL'}")
    
    if amazon_ok and suppliers_ok:
        print("\n🎉 All tests passed! API calls should work in Streamlit.")
    else:
        print("\n⚠️  Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
